# Email Server Docker Compose 配置
# 部署命令: docker-compose up -d

services:
  # 后端服务
  backend:
    build:
      context: ./src/backend # Dockerfile 路径
      dockerfile: Dockerfile
    container_name: email_server_backend # 容器名称
    ports:
      - "${BACKEND_PORT:-5555}:5555" # 映射端口：宿主机:容器 (可通过.env配置)
    user: "1001:1001" # 使用非root用户提高安全性
    environment:
      SQLITE_FILE: "/data/database.db" # SQLite 数据库文件路径 (容器内)
      GIN_MODE: "release"  # Gin框架生产模式
      # 请在 .env 文件中配置以下环境变量
      JWT_SECRET: "${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}"
      JWT_EXPIRES_IN: "${JWT_EXPIRES_IN:-24}"
      LINUXDO_CLIENT_ID: "${LINUXDO_CLIENT_ID}"
      LINUXDO_CLIENT_SECRET: "${LINUXDO_CLIENT_SECRET}"
      LINUXDO_REDIRECT_URI: "${LINUXDO_REDIRECT_URI:-http://localhost:5555/api/v1/auth/oauth2/linuxdo/callback}"
      LINUXDO_AUTH_URL: "${LINUXDO_AUTH_URL:-https://connect.linux.do/oauth2/authorize}"
      LINUXDO_TOKEN_URL: "${LINUXDO_TOKEN_URL:-https://connect.linux.do/oauth2/token}"
      LINUXDO_USER_INFO_URL: "${LINUXDO_USER_INFO_URL:-https://connect.linux.do/api/user}"
      # Google OAuth2 配置
      GOOGLE_CLIENT_ID: "${GOOGLE_CLIENT_ID}"
      GOOGLE_CLIENT_SECRET: "${GOOGLE_CLIENT_SECRET}"
      GOOGLE_REDIRECT_URI: "${GOOGLE_REDIRECT_URI:-http://localhost:5555/api/v1/oauth2/callback/google}"
      # Microsoft OAuth2 配置
      MICROSOFT_CLIENT_ID: "${MICROSOFT_CLIENT_ID}"
      MICROSOFT_CLIENT_SECRET: "${MICROSOFT_CLIENT_SECRET}"
      MICROSOFT_REDIRECT_URI: "${MICROSOFT_REDIRECT_URI:-http://localhost:5555/api/v1/oauth2/callback/microsoft}"
      # 其他配置
      FRONTEND_BASE_URL: "${FRONTEND_BASE_URL:-http://localhost:8080}"
      BACKEND_BASE_URL: "${BACKEND_BASE_URL:-http://localhost:5555}"
      ENCRYPTION_KEY: "${ENCRYPTION_KEY:-12345678901234567890123456789012}"
    volumes:
      - ./data/backend:/data # 持久化数据库文件：宿主机路径:容器内路径
      # 或者使用Docker管理的volume（推荐）:
      # - email_server_data:/data
    restart: unless-stopped # 容器退出时自动重启
    networks:
      - email_server_network # 连接到自定义网络
    # 健康检查
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--quiet", "-O", "/dev/null", "http://localhost:5555/api/v1/health", "||", "exit", "1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 前端服务
  frontend:
    build:
      context: ./src/frontend # Dockerfile 路径
      dockerfile: Dockerfile
      args:
        # 构建时注入环境变量，用于API基础URL
        # 请在.env文件中配置您的实际域名
        VUE_APP_API_BASE_URL: "${VUE_APP_API_BASE_URL:-http://localhost:5555/api/v1}"
    container_name: email_server_frontend # 容器名称
    ports:
      - "${FRONTEND_PORT:-80}:80" # 映射端口：宿主机:容器 (可通过.env配置)
    restart: unless-stopped # 容器退出时自动重启
    depends_on:
      - backend # 简化依赖，不等待健康检查
    networks:
      - email_server_network # 连接到自定义网络
    # 资源限制
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

# 定义网络
networks:
  email_server_network: # 自定义桥接网络
    driver: bridge

# 定义数据卷（可选，用于替代宿主机目录挂载）
# volumes:
#   email_server_data:
#     driver: local
