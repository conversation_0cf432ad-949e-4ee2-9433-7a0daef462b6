/* === CSS Utility Classes === */
/* These classes provide consistent styling patterns across the application */

/* === Layout Utilities === */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }

/* === Spacing Utilities === */
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-6 { padding: var(--space-6); }

.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-6 { margin: var(--space-6); }

.mb-2 { margin-bottom: var(--space-2); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }

.mt-2 { margin-top: var(--space-2); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }

/* === Typography Utilities === */
.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }

.font-normal { font-weight: var(--font-normal); }
.font-medium { font-weight: var(--font-medium); }
.font-semibold { font-weight: var(--font-semibold); }
.font-bold { font-weight: var(--font-bold); }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* === Color Utilities === */
.text-gray-500 { color: var(--color-gray-500); }
.text-gray-600 { color: var(--color-gray-600); }
.text-gray-700 { color: var(--color-gray-700); }
.text-gray-800 { color: var(--color-gray-800); }

.text-primary-500 { color: var(--color-primary-500); }
.text-primary-600 { color: var(--color-primary-600); }

.bg-white { background-color: #ffffff; }
.bg-gray-50 { background-color: var(--color-gray-50); }
.bg-gray-100 { background-color: var(--color-gray-100); }

/* === Border Utilities === */
.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-base); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

.border { border: 1px solid var(--color-gray-200); }
.border-gray-200 { border-color: var(--color-gray-200); }
.border-primary-200 { border-color: var(--color-primary-200); }

/* === Shadow Utilities === */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-base); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* === Transition Utilities === */
.transition { transition: all var(--transition-base); }
.transition-colors { transition: color var(--transition-base), background-color var(--transition-base), border-color var(--transition-base); }
.transition-transform { transition: transform var(--transition-base); }

/* === Interactive Utilities === */
.hover\:shadow-md:hover { box-shadow: var(--shadow-md); }
.hover\:shadow-lg:hover { box-shadow: var(--shadow-lg); }
.hover\:scale-105:hover { transform: scale(1.05); }
.hover\:translate-y-1:hover { transform: translateY(-4px); }

/* === Enhanced Card Utility === */
.card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-base);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary-500), var(--color-success-500), var(--color-warning-500));
  opacity: 0;
  transition: opacity var(--transition-base);
}

.card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px) scale(1.02);
  border-color: var(--color-primary-200);
}

.card:hover::before {
  opacity: 1;
}

/* === Enhanced Button Utility === */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: var(--font-semibold);
  font-size: var(--text-sm);
  transition: all var(--transition-base);
  border: none;
  cursor: pointer;
  text-decoration: none;
}

.btn-primary {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.btn-secondary {
  background: var(--color-gray-100);
  color: var(--color-gray-700);
  border: 1px solid var(--color-gray-200);
}

.btn-secondary:hover {
  background: var(--color-gray-200);
  border-color: var(--color-gray-300);
}

/* === Enhanced Input Utility === */
.input-enhanced {
  border-radius: var(--radius-lg);
  border: 2px solid var(--color-gray-200);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  transition: all var(--transition-base);
  box-shadow: var(--shadow-sm);
}

.input-enhanced:hover {
  border-color: var(--color-primary-300);
  background: rgba(255, 255, 255, 0.95);
  box-shadow: var(--shadow-md);
}

.input-enhanced:focus {
  border-color: var(--color-primary-500);
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

/* === Loading Animation Utility === */
.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* === Gradient Text Utility === */
.gradient-text {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-500));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* === Glass Effect Utility === */
.glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* === Responsive Utilities === */
@media (max-width: 768px) {
  .mobile\:hidden {
    display: none;
  }
  
  .mobile\:text-sm {
    font-size: var(--text-sm);
  }
  
  .mobile\:p-4 {
    padding: var(--space-4);
  }
}

/* Global Pagination Styles */
.el-pagination {
  margin-top: 0px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  padding: 0;
}

/* Ensure .pagination-container (if used as a wrapper) also allows flex behavior */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  padding: 0;
  margin-top: 2px;
  flex-shrink: 0;
}
