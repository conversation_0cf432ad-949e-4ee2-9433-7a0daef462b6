<template>
  <div class="oauth2-token-status">
    <el-card class="status-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="title">
            <el-icon><Key /></el-icon>
            OAuth2令牌状态
          </span>
          <div class="header-buttons">
            <el-button
              type="primary"
              size="small"
              @click="refreshAllStatus"
              :loading="refreshing"
            >
              <el-icon><Refresh /></el-icon>
              刷新状态
            </el-button>

            <el-button
              type="info"
              size="small"
              @click="showProviderDebugInfo"
            >
              <el-icon><Setting /></el-icon>
              系统调试
            </el-button>
          </div>
        </div>
      </template>

      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="3" animated />
      </div>

      <div v-else-if="tokenStatuses.length === 0" class="empty-container">
        <el-empty description="暂无OAuth2邮箱账户" />
      </div>

      <div v-else class="status-list">
        <div 
          v-for="status in tokenStatuses" 
          :key="status.accountId"
          class="status-item"
        >
          <div class="account-info">
            <div class="account-header">
              <span class="email">{{ status.email }}</span>
              <el-tag 
                :type="getStatusTagType(status.status)"
                size="small"
              >
                {{ getStatusText(status.status) }}
              </el-tag>
            </div>
            <div class="provider-info">
              <el-icon><Message /></el-icon>
              <span>{{ getProviderDisplayName(status.provider) }}</span>
              <span class="account-id">ID: {{ status.accountId }}</span>
            </div>
          </div>

          <div class="status-actions">
            <el-button 
              size="small" 
              @click="checkSingleStatus(status.accountId)"
              :loading="status.checking"
            >
              <el-icon><Search /></el-icon>
              检查
            </el-button>
            
            <el-button 
              v-if="status.status === 'expired' || status.status === 'error'"
              type="warning" 
              size="small"
              @click="reauthorize(status.provider, status.accountId)"
            >
              <el-icon><Refresh /></el-icon>
              重新授权
            </el-button>
          </div>
        </div>
      </div>

      <div class="status-summary">
        <el-descriptions :column="3" size="small" border>
          <el-descriptions-item label="总账户数">
            {{ tokenStatuses.length }}
          </el-descriptions-item>
          <el-descriptions-item label="正常账户">
            <el-tag type="success" size="small">
              {{ validCount }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="需要重新授权">
            <el-tag type="danger" size="small">
              {{ expiredCount }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Key, Refresh, Message, Search, Setting } from '@element-plus/icons-vue'
import { emailAccountAPI, checkOAuth2TokenStatus, oauth2API } from '@/utils/api'

const loading = ref(false)
const refreshing = ref(false)
const tokenStatuses = ref([])

// 计算属性
const validCount = computed(() => 
  tokenStatuses.value.filter(s => s.status === 'valid').length
)

const expiredCount = computed(() => 
  tokenStatuses.value.filter(s => s.status === 'expired' || s.status === 'error').length
)

// 获取状态标签类型
const getStatusTagType = (status) => {
  switch (status) {
    case 'valid': return 'success'
    case 'expired': return 'danger'
    case 'error': return 'warning'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'valid': return '正常'
    case 'expired': return '已过期'
    case 'error': return '错误'
    case 'checking': return '检查中...'
    default: return '未知'
  }
}

// 获取提供商显示名称
const getProviderDisplayName = (provider) => {
  const names = {
    google: 'Google',
    microsoft: 'Microsoft',
    outlook: 'Outlook'
  }
  return names[provider] || provider
}

// 加载OAuth2邮箱账户
const loadOAuth2Accounts = async () => {
  try {
    loading.value = true
    const response = await emailAccountAPI.getConfigured()
    // getConfigured使用标准API，拦截器返回{data: [...], meta: {...}}
    console.log('🔐 原始API响应:', response)

    // 根据用户提供的数据，API返回的是{data: [...], meta: {...}}格式
    const accounts = response.data || []
    console.log('🔐 加载的邮箱账户:', accounts)
    console.log('🔐 账户数量:', accounts.length)

    // 过滤出OAuth2账户（使用is_oauth_connected字段）
    const oauth2Accounts = accounts.filter(account => {
      console.log(`🔐 账户 ${account.email_address}: is_oauth_connected = ${account.is_oauth_connected} (类型: ${typeof account.is_oauth_connected})`)
      return account.is_oauth_connected === true
    })

    console.log('🔐 过滤后的OAuth2账户:', oauth2Accounts)
    console.log('🔐 OAuth2账户数量:', oauth2Accounts.length)

    // 额外调试：显示所有账户的OAuth连接状态
    console.log('🔐 所有账户的OAuth连接状态:')
    accounts.forEach((account, index) => {
      console.log(`  ${index + 1}. ${account.email_address}: ${account.is_oauth_connected} (${typeof account.is_oauth_connected})`)
    })

    tokenStatuses.value = oauth2Accounts.map(account => ({
      accountId: account.id,
      email: account.email_address,
      provider: detectProvider(account.provider, account.email_address),
      status: 'unknown',
      checking: false,
      message: ''
    }))

    console.log('🔐 设置的令牌状态:', tokenStatuses.value)

    // 自动检查所有账户的状态
    if (tokenStatuses.value.length > 0) {
      await checkAllStatuses()
    }
  } catch (error) {
    console.error('加载OAuth2账户失败:', error)
    ElMessage.error('加载OAuth2账户失败')
  } finally {
    loading.value = false
  }
}

// 检测提供商类型
const detectProvider = (providerString, emailAddress = '') => {
  if (!providerString) {
    // 如果没有provider信息，尝试从邮箱地址推断
    if (emailAddress) {
      const emailLower = emailAddress.toLowerCase()
      if (emailLower.includes('@gmail.com') || emailLower.includes('@googlemail.com')) {
        return 'google'
      } else if (emailLower.includes('@outlook.com') || emailLower.includes('@hotmail.com') || emailLower.includes('@live.com')) {
        return 'microsoft'
      }
    }
    return 'unknown'
  }

  const lower = providerString.toLowerCase()
  if (lower.includes('google') || lower.includes('gmail')) {
    return 'google'
  } else if (lower.includes('microsoft') || lower.includes('outlook') || lower.includes('hotmail')) {
    return 'microsoft'
  }

  // 如果provider字段无法识别，尝试从邮箱地址推断
  if (emailAddress) {
    const emailLower = emailAddress.toLowerCase()
    if (emailLower.includes('@gmail.com') || emailLower.includes('@googlemail.com')) {
      return 'google'
    } else if (emailLower.includes('@outlook.com') || emailLower.includes('@hotmail.com') || emailLower.includes('@live.com')) {
      return 'microsoft'
    }
  }

  return providerString
}

// 检查所有账户状态
const checkAllStatuses = async () => {
  const promises = tokenStatuses.value.map(status => 
    checkSingleStatus(status.accountId, false)
  )
  await Promise.all(promises)
}

// 检查单个账户状态
const checkSingleStatus = async (accountId, showMessage = true) => {
  const statusItem = tokenStatuses.value.find(s => s.accountId === accountId)
  if (!statusItem) return
  
  try {
    statusItem.checking = true
    statusItem.status = 'checking'
    
    const response = await checkOAuth2TokenStatus(accountId)
    // 令牌状态检查API直接返回状态数据，不使用标准响应格式
    const { status, message } = response

    console.log('🔐 令牌状态检查响应:', response)
    
    statusItem.status = status
    statusItem.message = message
    
    if (showMessage) {
      if (status === 'valid') {
        ElMessage.success(message || '令牌状态正常')
      } else {
        ElMessage.warning(message || '令牌需要处理')
      }
    }
  } catch (error) {
    console.error(`检查账户 ${accountId} 状态失败:`, error)
    statusItem.status = 'error'
    statusItem.message = error.message || '检查失败'
    
    if (showMessage) {
      ElMessage.error('检查令牌状态失败')
    }
  } finally {
    statusItem.checking = false
  }
}

// 刷新所有状态
const refreshAllStatus = async () => {
  refreshing.value = true
  try {
    await checkAllStatuses()
    ElMessage.success('状态刷新完成')
  } catch (error) {
    ElMessage.error('刷新状态失败')
  } finally {
    refreshing.value = false
  }
}

// 重新授权
const reauthorize = async (provider, accountId) => {
  try {
    await ElMessageBox.confirm(
      `确定要重新授权 ${getProviderDisplayName(provider)} 账户吗？`,
      '重新授权确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 确保provider不是unknown
    let finalProvider = provider
    if (provider === 'unknown') {
      // 尝试从tokenStatuses中的邮箱地址推断
      const statusItem = tokenStatuses.value.find(s => s.accountId === accountId)
      if (statusItem && statusItem.email) {
        finalProvider = detectProvider('', statusItem.email)
        console.log('🔐 重新推断的provider:', finalProvider)
      }
    }

    console.log('🔐 重新授权使用的provider:', finalProvider)

    // 使用API获取授权URL
    const response = await oauth2API.getConnectURL(finalProvider, accountId)
    console.log('🔐 OAuth2 API响应:', response)

    // 检查响应数据结构
    // API拦截器已经处理了响应，直接从response中获取auth_url
    if (response && response.auth_url) {
      const authUrl = response.auth_url
      console.log('🔐 提取的auth_url:', authUrl)

      if (authUrl) {
        // 在新窗口中打开授权页面
        const authWindow = window.open(
          authUrl,
          'oauth2_reauth',
          'width=600,height=700,scrollbars=yes,resizable=yes'
        )

        // 监听授权完成
        const checkClosed = setInterval(() => {
          if (authWindow.closed) {
            clearInterval(checkClosed)
            ElMessage.info('授权窗口已关闭，正在检查最新状态...')
            // 延迟检查状态，给服务器时间处理
            setTimeout(() => {
              checkSingleStatus(accountId, true)
            }, 2000)
          }
        }, 1000)
      } else {
        console.error('🔐 响应中没有找到auth_url:', response)
        ElMessage.error('无法获取重新授权链接，请稍后重试。')
      }
    } else {
      console.error('🔐 无效的API响应:', response)
      ElMessage.error('无法获取重新授权链接，请稍后重试。')
    }
  } catch (error) {
    // 用户取消了重新授权
    console.log('用户取消了重新授权')
  }
}

// 显示提供商调试信息
const showProviderDebugInfo = async () => {
  try {
    // 获取所有邮箱账户（不仅仅是已配置的）
    const allAccountsResponse = await emailAccountAPI.getAll()
    const allAccounts = allAccountsResponse.data || []

    // 获取已配置的邮箱账户
    const configuredResponse = await emailAccountAPI.getConfigured()
    const configuredAccounts = configuredResponse.data || []

    // 获取OAuth2提供商信息
    const providersResponse = await oauth2API.getProviders()
    const providers = providersResponse.data.providers || []

    let debugInfo = `=== 邮箱账户调试信息 ===\n\n`
    debugInfo += `所有邮箱账户数量: ${allAccounts.length}\n`
    debugInfo += `已配置邮箱账户数量: ${configuredAccounts.length}\n`
    debugInfo += `OAuth2账户数量: ${configuredAccounts.filter(acc => acc.is_oauth_connected).length}\n\n`

    debugInfo += `=== 所有邮箱账户详情 ===\n`
    allAccounts.forEach((account, index) => {
      debugInfo += `${index + 1}. ${account.email_address}\n`
      debugInfo += `   ID: ${account.id}\n`
      debugInfo += `   Provider: ${account.provider || '未设置'}\n`
      debugInfo += `   IMAP: ${account.imap_server || '未配置'}:${account.imap_port || 0}\n`
      debugInfo += `   OAuth连接: ${account.is_oauth_connected ? '是' : '否'}\n`
      debugInfo += `   有密码: ${account.has_password ? '是' : '否'}\n\n`
    })

    debugInfo += `=== OAuth2提供商配置 (${providers.length}个) ===\n`
    providers.forEach((provider, index) => {
      debugInfo += `${index + 1}. ${provider.name}\n`
      debugInfo += `   Client ID: ${provider.client_id}\n`
      debugInfo += `   Auth URL: ${provider.auth_url}\n`
      debugInfo += `   Token URL: ${provider.token_url}\n`
      debugInfo += `   Scopes: ${provider.scopes}\n`
      debugInfo += `   Has Secret: ${provider.has_secret ? '是' : '否'}\n`
      if (provider.imap_server) {
        debugInfo += `   IMAP: ${provider.imap_server}:${provider.imap_port}\n`
      }
      debugInfo += '\n'
    })

    await ElMessageBox.alert(debugInfo, '系统调试信息', {
      confirmButtonText: '确定',
      type: 'info',
      customStyle: {
        width: '700px',
        maxHeight: '80vh',
        overflow: 'auto'
      }
    })
  } catch (error) {
    console.error('获取调试信息失败:', error)
    ElMessage.error('获取调试信息失败')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadOAuth2Accounts()
})
</script>

<style scoped>
.oauth2-token-status {
  margin: 20px 0;
}

.status-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-buttons {
  display: flex;
  gap: 8px;
}

.title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.loading-container,
.empty-container {
  padding: 20px;
  text-align: center;
}

.status-list {
  margin-bottom: 20px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 12px;
  transition: all 0.3s;
}

.status-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.account-info {
  flex: 1;
}

.account-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.email {
  font-weight: 600;
  color: #303133;
}

.provider-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
  font-size: 14px;
}

.account-id {
  color: #909399;
  font-size: 12px;
}

.status-actions {
  display: flex;
  gap: 8px;
}

.status-summary {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}
</style>
