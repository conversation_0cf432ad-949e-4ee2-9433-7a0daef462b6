doc/
# 环境变量文件
.env
.env.local
.env.production
.env.development

# 数据库文件
*.db
*.sqlite
*.sqlite3
src/backend/gorm.db
data/backend/database.db

# 数据目录
data/

# 日志文件
*.log
logs/
backups/

# Go相关
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out

# Dependency directories
vendor/

# Go workspace file
go.work

# Node.js相关
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Cache directories
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 构建输出
dist/
build/
out/
dist_backend/
dist_frontend/

# 备份文件
*.bak
*.backup

# 证书文件
*.pem
*.key
*.crt
*.csr

# 临时文件
*.tmp
*.temp

# 测试覆盖率
.coverage
.coverage.*

# 本地配置文件
config.local.js
config.local.json

# 压缩文件
*.zip
*.tar.gz
*.rar

# 系统文件
.fuse_hidden*
.directory
.Trash-*
.nfs*