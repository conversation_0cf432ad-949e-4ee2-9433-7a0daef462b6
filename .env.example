# 生产环境配置文件
# 复制此文件为 .env 并修改相应配置

# ========== 端口配置 ==========
# 前端端口 (默认: 开发环境8081, 生产环境80)
FRONTEND_PORT=80
# 后端端口 (默认: 5555)
BACKEND_PORT=5555
# HTTPS端口 (可选, 默认: 443)
# FRONTEND_HTTPS_PORT=443

# ========== 数据库配置 ==========
SQLITE_FILE=/data/database.db

# ========== JWT配置 ==========
# 生产环境必须修改为强密钥 (至少32个字符)
JWT_SECRET=your-production-super-secret-jwt-key-at-least-32-characters-long
JWT_EXPIRES_IN=24

# ========== LinuxDo OAuth2 配置 ==========
# 请到 https://connect.linux.do 申请应用获取以下信息
LINUXDO_CLIENT_ID=your_client_id
LINUXDO_CLIENT_SECRET=your_client_secret
# 修改为您的实际域名
LINUXDO_REDIRECT_URI=https://yourdomain.com/api/v1/auth/oauth2/linuxdo/callback
LINUXDO_AUTH_URL=https://connect.linux.do/oauth2/authorize
LINUXDO_TOKEN_URL=https://connect.linux.do/oauth2/token
LINUXDO_USER_INFO_URL=https://connect.linux.do/api/user

# ========== 前端配置 ==========
# 修改为您的实际API地址 
VUE_APP_API_BASE_URL=http://yourdomain.com:5555/api/v1
# 前端基础URL，用于OAuth2回调重定向
FRONTEND_BASE_URL=https://yourdomain.com

# ========== 安全配置 ==========
# 生产环境建议启用
SECURE_COOKIES=true
CORS_ORIGINS=https://yourdomain.com

# ========== 其他配置 ==========
# Gin框架模式
GIN_MODE=release

# ========== Email Provider OAuth2 Settings ==========
# --- Google ---
# Get your credentials from the Google Cloud Console
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_REDIRECT_URI=http://localhost:5555/api/v1/oauth2/callback/google

# --- Microsoft ---
# Get your credentials from the Azure Portal
MICROSOFT_CLIENT_ID=
MICROSOFT_CLIENT_SECRET=
MICROSOFT_REDIRECT_URI=http://localhost:5555/api/v1/oauth2/callback/microsoft

BACKEND_BASE_URL=http://localhost:5555

# --- Security Settings ---
# IMPORTANT: This key MUST be 32 bytes long for AES-256.
ENCRYPTION_KEY=12345678901234567890123456789012
